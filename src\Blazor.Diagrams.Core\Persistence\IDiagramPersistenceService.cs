using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Blazor.Diagrams.Core.Persistence;

/// <summary>
/// High-level service for persisting and loading complete diagrams
/// </summary>
public interface IDiagramPersistenceService
{
    /// <summary>
    /// Save a complete diagram with all its elements
    /// </summary>
    Task<string> SaveDiagramAsync(Diagram diagram, string? name = null, string? description = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Load a complete diagram by ID
    /// </summary>
    Task<Diagram?> LoadDiagramAsync(string diagramId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get all saved diagrams metadata
    /// </summary>
    Task<IEnumerable<DiagramMetadata>> GetDiagramsMetadataAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Delete a diagram and all its elements
    /// </summary>
    Task DeleteDiagramAsync(string diagramId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Update diagram metadata
    /// </summary>
    Task UpdateDiagramMetadataAsync(string diagramId, string? name = null, string? description = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Clone a diagram
    /// </summary>
    Task<string> CloneDiagramAsync(string diagramId, string? newName = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// Metadata for a saved diagram
/// </summary>
public class DiagramMetadata
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int NodeCount { get; set; }
    public int LinkCount { get; set; }
    public int GroupCount { get; set; }
}
