using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Blazor.Diagrams.Core.Persistence;

/// <summary>
/// Database entity representing a diagram group
/// </summary>
public class GroupEntity : BaseEntity
{
    [Required]
    public string DiagramId { get; set; } = string.Empty;
    
    [MaxLength(200)]
    public string? Title { get; set; }
    
    /// <summary>
    /// Group type for polymorphic deserialization
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string GroupType { get; set; } = string.Empty;
    
    /// <summary>
    /// Position X coordinate
    /// </summary>
    public double PositionX { get; set; }
    
    /// <summary>
    /// Position Y coordinate
    /// </summary>
    public double PositionY { get; set; }
    
    /// <summary>
    /// Width of the group
    /// </summary>
    public double? Width { get; set; }
    
    /// <summary>
    /// Height of the group
    /// </summary>
    public double? Height { get; set; }
    
    /// <summary>
    /// Padding around child nodes
    /// </summary>
    public byte Padding { get; set; } = 30;
    
    /// <summary>
    /// Whether the group auto-sizes based on children
    /// </summary>
    public bool AutoSize { get; set; } = true;
    
    /// <summary>
    /// Whether the group is locked
    /// </summary>
    public bool Locked { get; set; }
    
    /// <summary>
    /// Whether the group is visible
    /// </summary>
    public bool Visible { get; set; } = true;
    
    /// <summary>
    /// Whether the group is selected
    /// </summary>
    public bool Selected { get; set; }
    
    /// <summary>
    /// Parent group ID if this group is nested
    /// </summary>
    public string? ParentGroupId { get; set; }
    
    /// <summary>
    /// Custom properties as JSON
    /// </summary>
    public string? PropertiesJson { get; set; }
    
    /// <summary>
    /// Navigation properties
    /// </summary>
    [ForeignKey(nameof(DiagramId))]
    public virtual DiagramEntity? Diagram { get; set; }
    
    [ForeignKey(nameof(ParentGroupId))]
    public virtual GroupEntity? ParentGroup { get; set; }
    
    public virtual ICollection<NodeEntity> ChildNodes { get; set; } = new List<NodeEntity>();
    public virtual ICollection<GroupEntity> ChildGroups { get; set; } = new List<GroupEntity>();
}
