using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Blazor.Diagrams.Core.Persistence;

/// <summary>
/// Database entity representing a complete diagram
/// </summary>
public class DiagramEntity : BaseEntity
{
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(1000)]
    public string? Description { get; set; }
    
    /// <summary>
    /// Serialized diagram options as JSON
    /// </summary>
    public string? OptionsJson { get; set; }
    
    /// <summary>
    /// Pan position X
    /// </summary>
    public double PanX { get; set; }
    
    /// <summary>
    /// Pan position Y
    /// </summary>
    public double PanY { get; set; }
    
    /// <summary>
    /// Zoom level
    /// </summary>
    public double Zoom { get; set; } = 1.0;
    
    /// <summary>
    /// Container bounds as JSON
    /// </summary>
    public string? ContainerJson { get; set; }
    
    /// <summary>
    /// Navigation properties
    /// </summary>
    public virtual ICollection<NodeEntity> Nodes { get; set; } = new List<NodeEntity>();
    public virtual ICollection<LinkEntity> Links { get; set; } = new List<LinkEntity>();
    public virtual ICollection<GroupEntity> Groups { get; set; } = new List<GroupEntity>();
}
