using Blazor.Diagrams.Core.Anchors;
using Blazor.Diagrams.Core.Geometry;
using Blazor.Diagrams.Core.Models;
using Blazor.Diagrams.Core.Models.Base;
using Blazor.Diagrams.Core.PathGenerators;
using Blazor.Diagrams.Core.Routers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;

namespace Blazor.Diagrams.Core.Persistence.Serialization;

/// <summary>
/// Default implementation of model serialization
/// </summary>
public class ModelSerializer : IModelSerializer
{
    private readonly JsonSerializerOptions _jsonOptions;

    public ModelSerializer()
    {
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        };
    }

    public DiagramEntity SerializeDiagram(Diagram diagram, string? name = null, string? description = null)
    {
        return new DiagramEntity
        {
            Id = Helpers.NanoidGenerator(),
            Name = name ?? $"Diagram {DateTime.Now:yyyy-MM-dd HH:mm}",
            Description = description,
            PanX = diagram.Pan.X,
            PanY = diagram.Pan.Y,
            Zoom = diagram.Zoom,
            ContainerJson = diagram.Container != null ? JsonSerializer.Serialize(diagram.Container, _jsonOptions) : null,
            OptionsJson = JsonSerializer.Serialize(diagram.Options, _jsonOptions)
        };
    }

    public Diagram DeserializeDiagram(DiagramEntity diagramEntity, IEnumerable<NodeEntity> nodes, 
        IEnumerable<LinkEntity> links, IEnumerable<GroupEntity> groups)
    {
        // Create diagram with default options first
        var diagram = new TestDiagram();
        
        // Restore pan and zoom
        diagram.SetPan(diagramEntity.PanX, diagramEntity.PanY);
        diagram.SetZoom(diagramEntity.Zoom);
        
        // Restore container if available
        if (!string.IsNullOrEmpty(diagramEntity.ContainerJson))
        {
            var container = JsonSerializer.Deserialize<Rectangle>(diagramEntity.ContainerJson, _jsonOptions);
            if (container != null)
            {
                diagram.SetContainer(container);
            }
        }

        return diagram;
    }

    public NodeEntity SerializeNode(NodeModel node, string diagramId)
    {
        return new NodeEntity
        {
            Id = node.Id,
            DiagramId = diagramId,
            Title = node.Title,
            NodeType = node.GetType().Name,
            PositionX = node.Position.X,
            PositionY = node.Position.Y,
            Width = node.Size?.Width,
            Height = node.Size?.Height,
            ControlledSize = node.ControlledSize,
            Locked = node.Locked,
            Visible = node.Visible,
            GroupId = node.Group?.Id,
            PropertiesJson = SerializeCustomProperties(node),
            CreatedAt = node.CreatedAt,
            UpdatedAt = node.UpdatedAt,
            Version = node.Version
        };
    }

    public NodeModel DeserializeNode(NodeEntity nodeEntity, IEnumerable<PortEntity> ports)
    {
        var position = new Point(nodeEntity.PositionX, nodeEntity.PositionY);
        var node = CreateNodeInstance(nodeEntity.NodeType, nodeEntity.Id, position);
        
        node.Title = nodeEntity.Title;
        node.Locked = nodeEntity.Locked;
        node.Visible = nodeEntity.Visible;
        
        if (nodeEntity.Width.HasValue && nodeEntity.Height.HasValue)
        {
            node.Size = new Size(nodeEntity.Width.Value, nodeEntity.Height.Value);
        }
        
        // Restore custom properties
        RestoreCustomProperties(node, nodeEntity.PropertiesJson);
        
        // Add ports
        foreach (var portEntity in ports.OrderBy(p => p.CreatedAt))
        {
            var port = DeserializePort(portEntity, node);
            node.AddPort(port);
        }
        
        return node;
    }

    public LinkEntity SerializeLink(BaseLinkModel link, string diagramId)
    {
        return new LinkEntity
        {
            Id = link.Id,
            DiagramId = diagramId,
            LinkType = link.GetType().Name,
            SourceAnchorJson = SerializeAnchor(link.Source),
            TargetAnchorJson = SerializeAnchor(link.Target),
            RouteJson = link.Route != null ? JsonSerializer.Serialize(link.Route, _jsonOptions) : null,
            RouterJson = link.Router != null ? SerializeRouter(link.Router) : null,
            PathGeneratorJson = link.PathGenerator != null ? SerializePathGenerator(link.PathGenerator) : null,
            SourceMarkerJson = link.SourceMarker != null ? JsonSerializer.Serialize(link.SourceMarker, _jsonOptions) : null,
            TargetMarkerJson = link.TargetMarker != null ? JsonSerializer.Serialize(link.TargetMarker, _jsonOptions) : null,
            Segmentable = link.Segmentable,
            Locked = link.Locked,
            Visible = link.Visible,
            PropertiesJson = SerializeCustomProperties(link),
            CreatedAt = link.CreatedAt,
            UpdatedAt = link.UpdatedAt,
            Version = link.Version
        };
    }

    public BaseLinkModel DeserializeLink(LinkEntity linkEntity, IEnumerable<LinkVertexEntity> vertices, 
        IEnumerable<LinkLabelEntity> labels, Dictionary<string, NodeModel> nodeMap)
    {
        var sourceAnchor = DeserializeAnchor(linkEntity.SourceAnchorJson, nodeMap);
        var targetAnchor = DeserializeAnchor(linkEntity.TargetAnchorJson, nodeMap);
        
        var link = CreateLinkInstance(linkEntity.LinkType, linkEntity.Id, sourceAnchor, targetAnchor);
        
        link.Segmentable = linkEntity.Segmentable;
        link.Locked = linkEntity.Locked;
        link.Visible = linkEntity.Visible;
        
        // Restore router and path generator
        if (!string.IsNullOrEmpty(linkEntity.RouterJson))
        {
            link.Router = DeserializeRouter(linkEntity.RouterJson);
        }
        
        if (!string.IsNullOrEmpty(linkEntity.PathGeneratorJson))
        {
            link.PathGenerator = DeserializePathGenerator(linkEntity.PathGeneratorJson);
        }
        
        // Restore custom properties
        RestoreCustomProperties(link, linkEntity.PropertiesJson);
        
        return link;
    }

    public GroupEntity SerializeGroup(GroupModel group, string diagramId)
    {
        return new GroupEntity
        {
            Id = group.Id,
            DiagramId = diagramId,
            Title = group.Title,
            GroupType = group.GetType().Name,
            PositionX = group.Position.X,
            PositionY = group.Position.Y,
            Width = group.Size?.Width,
            Height = group.Size?.Height,
            Padding = group.Padding,
            AutoSize = group.AutoSize,
            Locked = group.Locked,
            Visible = group.Visible,
            ParentGroupId = group.Group?.Id,
            PropertiesJson = SerializeCustomProperties(group),
            CreatedAt = group.CreatedAt,
            UpdatedAt = group.UpdatedAt,
            Version = group.Version
        };
    }

    public GroupModel DeserializeGroup(GroupEntity groupEntity, IEnumerable<NodeModel> childNodes)
    {
        var group = CreateGroupInstance(groupEntity.GroupType, groupEntity.Id, childNodes, 
            groupEntity.Padding, groupEntity.AutoSize);
        
        group.Title = groupEntity.Title;
        group.Locked = groupEntity.Locked;
        group.Visible = groupEntity.Visible;
        group.Position = new Point(groupEntity.PositionX, groupEntity.PositionY);
        
        if (groupEntity.Width.HasValue && groupEntity.Height.HasValue)
        {
            group.Size = new Size(groupEntity.Width.Value, groupEntity.Height.Value);
        }
        
        // Restore custom properties
        RestoreCustomProperties(group, groupEntity.PropertiesJson);
        
        return group;
    }

    public PortEntity SerializePort(PortModel port, string nodeId)
    {
        return new PortEntity
        {
            Id = port.Id,
            NodeId = nodeId,
            Alignment = port.Alignment.ToString(),
            PositionX = port.Position.X,
            PositionY = port.Position.Y,
            Width = port.Size.Width,
            Height = port.Size.Height,
            Locked = port.Locked,
            Visible = port.Visible,
            Initialized = port.Initialized,
            PropertiesJson = SerializeCustomProperties(port),
            CreatedAt = port.CreatedAt,
            UpdatedAt = port.UpdatedAt,
            Version = port.Version
        };
    }

    public PortModel DeserializePort(PortEntity portEntity, NodeModel parentNode)
    {
        var alignment = Enum.Parse<PortAlignment>(portEntity.Alignment);
        var position = new Point(portEntity.PositionX, portEntity.PositionY);
        var size = new Size(portEntity.Width, portEntity.Height);
        
        var port = CreatePortInstance(portEntity.Id, parentNode, alignment, position, size);
        port.Locked = portEntity.Locked;
        port.Visible = portEntity.Visible;
        port.Initialized = portEntity.Initialized;
        
        // Restore custom properties
        RestoreCustomProperties(port, portEntity.PropertiesJson);
        
        return port;
    }

    // Helper methods for creating instances and handling custom properties
    private NodeModel CreateNodeInstance(string nodeType, string id, Point position)
    {
        // This would be extended to handle custom node types
        return nodeType switch
        {
            nameof(NodeModel) => new NodeModel(id, position),
            nameof(GroupModel) => new GroupModel(Enumerable.Empty<NodeModel>()),
            _ => new NodeModel(id, position) // Default fallback
        };
    }

    private BaseLinkModel CreateLinkInstance(string linkType, string id, Anchor source, Anchor target)
    {
        return linkType switch
        {
            nameof(LinkModel) => new LinkModel(id, source, target),
            _ => new LinkModel(id, source, target) // Default fallback
        };
    }

    private GroupModel CreateGroupInstance(string groupType, string id, IEnumerable<NodeModel> children, 
        byte padding, bool autoSize)
    {
        return groupType switch
        {
            nameof(GroupModel) => new GroupModel(children, padding, autoSize),
            _ => new GroupModel(children, padding, autoSize) // Default fallback
        };
    }

    private PortModel CreatePortInstance(string id, NodeModel parent, PortAlignment alignment, Point position, Size size)
    {
        return new PortModel(id, parent, alignment, position, size);
    }

    private string SerializeCustomProperties(Model model)
    {
        // This would be extended to handle custom properties of different model types
        var properties = new Dictionary<string, object>();
        
        // Add type-specific properties here
        if (model is LinkModel linkModel)
        {
            if (!string.IsNullOrEmpty(linkModel.Color))
                properties["Color"] = linkModel.Color;
            if (!string.IsNullOrEmpty(linkModel.SelectedColor))
                properties["SelectedColor"] = linkModel.SelectedColor;
            if (linkModel.Width != 2.0)
                properties["Width"] = linkModel.Width;
        }
        
        return properties.Count > 0 ? JsonSerializer.Serialize(properties, _jsonOptions) : string.Empty;
    }

    private void RestoreCustomProperties(Model model, string? propertiesJson)
    {
        if (string.IsNullOrEmpty(propertiesJson))
            return;
            
        try
        {
            var properties = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(propertiesJson, _jsonOptions);
            if (properties == null) return;
            
            // Restore type-specific properties
            if (model is LinkModel linkModel)
            {
                if (properties.TryGetValue("Color", out var color))
                    linkModel.Color = color.GetString();
                if (properties.TryGetValue("SelectedColor", out var selectedColor))
                    linkModel.SelectedColor = selectedColor.GetString();
                if (properties.TryGetValue("Width", out var width))
                    linkModel.Width = width.GetDouble();
            }
        }
        catch (JsonException)
        {
            // Log error but don't fail the deserialization
        }
    }

    private string SerializeAnchor(Anchor anchor)
    {
        object anchorData = anchor switch
        {
            SinglePortAnchor spa => new { Type = anchor.GetType().Name, Data = new { PortId = spa.Port.Id } },
            PositionAnchor pa => new { Type = anchor.GetType().Name, Data = new { X = pa.GetPlainPosition()?.X ?? 0, Y = pa.GetPlainPosition()?.Y ?? 0 } },
            ShapeIntersectionAnchor sia => new { Type = anchor.GetType().Name, Data = new { NodeId = sia.Node.Id } },
            _ => new { Type = anchor.GetType().Name, Data = new { } }
        };
        
        return JsonSerializer.Serialize(anchorData, _jsonOptions);
    }

    private Anchor DeserializeAnchor(string anchorJson, Dictionary<string, NodeModel> nodeMap)
    {
        var anchorData = JsonSerializer.Deserialize<JsonElement>(anchorJson, _jsonOptions);
        var type = anchorData.GetProperty("Type").GetString();
        var data = anchorData.GetProperty("Data");
        
        return type switch
        {
            nameof(SinglePortAnchor) => CreateSinglePortAnchor(data, nodeMap),
            nameof(PositionAnchor) => new PositionAnchor(new Point(
                data.GetProperty("X").GetDouble(),
                data.GetProperty("Y").GetDouble())),
            nameof(ShapeIntersectionAnchor) => CreateShapeIntersectionAnchor(data, nodeMap),
            _ => new PositionAnchor(Point.Zero)
        };
    }

    private SinglePortAnchor CreateSinglePortAnchor(JsonElement data, Dictionary<string, NodeModel> nodeMap)
    {
        var portId = data.GetProperty("PortId").GetString();
        if (string.IsNullOrEmpty(portId)) 
            return new SinglePortAnchor(new PortModel(nodeMap.Values.First(), PortAlignment.Bottom));
            
        var port = nodeMap.Values.SelectMany(n => n.Ports).FirstOrDefault(p => p.Id == portId);
        return port != null ? new SinglePortAnchor(port) : 
            new SinglePortAnchor(new PortModel(nodeMap.Values.First(), PortAlignment.Bottom));
    }

    private ShapeIntersectionAnchor CreateShapeIntersectionAnchor(JsonElement data, Dictionary<string, NodeModel> nodeMap)
    {
        var nodeId = data.GetProperty("NodeId").GetString();
        if (string.IsNullOrEmpty(nodeId) || !nodeMap.TryGetValue(nodeId, out var node))
            return new ShapeIntersectionAnchor(nodeMap.Values.First());
            
        return new ShapeIntersectionAnchor(node);
    }

    private string SerializeRouter(Router router)
    {
        var routerData = new
        {
            Type = router.GetType().Name,
            // Add router-specific properties here
        };
        
        return JsonSerializer.Serialize(routerData, _jsonOptions);
    }

    private Router DeserializeRouter(string routerJson)
    {
        var routerData = JsonSerializer.Deserialize<JsonElement>(routerJson, _jsonOptions);
        var type = routerData.GetProperty("Type").GetString();
        
        return type switch
        {
            nameof(NormalRouter) => new NormalRouter(),
            nameof(OrthogonalRouter) => new OrthogonalRouter(),
            _ => new NormalRouter()
        };
    }

    private string SerializePathGenerator(PathGenerator pathGenerator)
    {
        var generatorData = new
        {
            Type = pathGenerator.GetType().Name,
            // Add path generator-specific properties here
        };
        
        return JsonSerializer.Serialize(generatorData, _jsonOptions);
    }

    private PathGenerator DeserializePathGenerator(string pathGeneratorJson)
    {
        var generatorData = JsonSerializer.Deserialize<JsonElement>(pathGeneratorJson, _jsonOptions);
        var type = generatorData.GetProperty("Type").GetString();
        
        return type switch
        {
            nameof(SmoothPathGenerator) => new SmoothPathGenerator(),
            nameof(StraightPathGenerator) => new StraightPathGenerator(),
            _ => new SmoothPathGenerator()
        };
    }
}

// Simple test diagram implementation for deserialization
internal class TestDiagram : Diagram
{
    public TestDiagram() : base(registerDefaultBehaviors: false)
    {
        Options = new TestDiagramOptions();
    }

    public override Options.DiagramOptions Options { get; }

    public new void SetPan(double x, double y)
    {
        // Implementation would set pan
    }

    public new void SetZoom(double zoom)
    {
        // Implementation would set zoom
    }

    public new void SetContainer(Rectangle container)
    {
        // Implementation would set container
    }
}

internal class TestDiagramOptions : Options.DiagramOptions
{
    // Test implementation
}
