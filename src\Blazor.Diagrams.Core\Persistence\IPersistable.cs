using System;

namespace Blazor.Diagrams.Core.Persistence;

/// <summary>
/// Interface for models that can be persisted to a database
/// </summary>
public interface IPersistable
{
    /// <summary>
    /// Unique identifier for the entity
    /// </summary>
    string Id { get; }
    
    /// <summary>
    /// When the entity was created
    /// </summary>
    DateTime CreatedAt { get; set; }
    
    /// <summary>
    /// When the entity was last updated
    /// </summary>
    DateTime UpdatedAt { get; set; }
    
    /// <summary>
    /// Version for optimistic concurrency control
    /// </summary>
    int Version { get; set; }
}
