﻿using Blazor.Diagrams.Core.Geometry;

namespace Blazor.Diagrams.Core.Events;

//public record MouseEventArgs(double ClientX, double ClientY, long Button, long Buttons, bool Ctrl<PERSON>ey, bool Shift<PERSON>ey, bool <PERSON><PERSON>);
public record MouseEventArgs(double ClientX, double ClientY, long Button, long Buttons, bool Ctrl<PERSON>ey, bool <PERSON>ft<PERSON>ey, bool <PERSON>)
{
    public Point Client => new(ClientX, ClientY);
}