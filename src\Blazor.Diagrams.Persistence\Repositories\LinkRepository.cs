using Blazor.Diagrams.Core.Persistence;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Blazor.Diagrams.Persistence.Repositories;

/// <summary>
/// Specialized repository for LinkEntity with additional methods
/// </summary>
public class LinkRepository : Repository<LinkEntity>
{
    public LinkRepository(DiagramDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Get all links for a specific diagram with their vertices and labels
    /// </summary>
    public async Task<IEnumerable<LinkEntity>> GetByDiagramIdWithDetailsAsync(string diagramId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(l => l.Vertices.OrderBy(v => v.Order))
            .Include(l => l.Labels)
            .Where(l => l.DiagramId == diagramId)
            .OrderBy(l => l.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get links that reference a specific node (through anchors)
    /// </summary>
    public async Task<IEnumerable<LinkEntity>> GetByNodeIdAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(l => l.Vertices.OrderBy(v => v.Order))
            .Include(l => l.Labels)
            .Where(l => l.SourceAnchorJson.Contains(nodeId) || l.TargetAnchorJson.Contains(nodeId))
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Delete all links for a specific diagram
    /// </summary>
    public async Task DeleteByDiagramIdAsync(string diagramId, CancellationToken cancellationToken = default)
    {
        var links = await _dbSet
            .Where(l => l.DiagramId == diagramId)
            .ToListAsync(cancellationToken);

        _dbSet.RemoveRange(links);
    }

    /// <summary>
    /// Get link count by diagram
    /// </summary>
    public async Task<int> CountByDiagramIdAsync(string diagramId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(l => l.DiagramId == diagramId, cancellationToken);
    }

    /// <summary>
    /// Get links with specific router type
    /// </summary>
    public async Task<IEnumerable<LinkEntity>> GetByRouterTypeAsync(string diagramId, string routerType, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(l => l.DiagramId == diagramId && l.RouterJson != null && l.RouterJson.Contains(routerType))
            .ToListAsync(cancellationToken);
    }
}
