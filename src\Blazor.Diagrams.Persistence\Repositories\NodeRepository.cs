using Blazor.Diagrams.Core.Persistence;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Blazor.Diagrams.Persistence.Repositories;

/// <summary>
/// Specialized repository for NodeEntity with additional methods
/// </summary>
public class NodeRepository : Repository<NodeEntity>
{
    public NodeRepository(DiagramDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Get all nodes for a specific diagram with their ports
    /// </summary>
    public async Task<IEnumerable<NodeEntity>> GetByDiagramIdWithPortsAsync(string diagramId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(n => n.Ports)
            .Where(n => n.DiagramId == diagramId)
            .OrderBy(n => n.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get nodes by group ID
    /// </summary>
    public async Task<IEnumerable<NodeEntity>> GetByGroupIdAsync(string groupId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(n => n.Ports)
            .Where(n => n.GroupId == groupId)
            .OrderBy(n => n.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Get nodes without a group (top-level nodes)
    /// </summary>
    public async Task<IEnumerable<NodeEntity>> GetTopLevelNodesByDiagramIdAsync(string diagramId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(n => n.Ports)
            .Where(n => n.DiagramId == diagramId && n.GroupId == null)
            .OrderBy(n => n.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Delete all nodes for a specific diagram
    /// </summary>
    public async Task DeleteByDiagramIdAsync(string diagramId, CancellationToken cancellationToken = default)
    {
        var nodes = await _dbSet
            .Where(n => n.DiagramId == diagramId)
            .ToListAsync(cancellationToken);

        _dbSet.RemoveRange(nodes);
    }

    /// <summary>
    /// Get node count by diagram
    /// </summary>
    public async Task<int> CountByDiagramIdAsync(string diagramId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(n => n.DiagramId == diagramId, cancellationToken);
    }
}
