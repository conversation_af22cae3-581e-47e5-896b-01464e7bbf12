using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Blazor.Diagrams.Core.Persistence;

/// <summary>
/// Database entity representing a diagram link
/// </summary>
public class LinkEntity : BaseEntity
{
    [Required]
    public string DiagramId { get; set; } = string.Empty;
    
    /// <summary>
    /// Link type for polymorphic deserialization
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string LinkType { get; set; } = string.Empty;
    
    /// <summary>
    /// Source anchor information as JSON
    /// </summary>
    [Required]
    public string SourceAnchorJson { get; set; } = string.Empty;
    
    /// <summary>
    /// Target anchor information as JSON
    /// </summary>
    [Required]
    public string TargetAnchorJson { get; set; } = string.Empty;
    
    /// <summary>
    /// Route points as JSON array
    /// </summary>
    public string? RouteJson { get; set; }
    
    /// <summary>
    /// Router type and configuration as JSON
    /// </summary>
    public string? RouterJson { get; set; }
    
    /// <summary>
    /// Path generator type and configuration as JSON
    /// </summary>
    public string? PathGeneratorJson { get; set; }
    
    /// <summary>
    /// Source marker configuration as JSON
    /// </summary>
    public string? SourceMarkerJson { get; set; }
    
    /// <summary>
    /// Target marker configuration as JSON
    /// </summary>
    public string? TargetMarkerJson { get; set; }
    
    /// <summary>
    /// Link color
    /// </summary>
    [MaxLength(50)]
    public string? Color { get; set; }
    
    /// <summary>
    /// Selected color
    /// </summary>
    [MaxLength(50)]
    public string? SelectedColor { get; set; }
    
    /// <summary>
    /// Link width
    /// </summary>
    public double Width { get; set; } = 2.0;
    
    /// <summary>
    /// Whether the link is segmentable
    /// </summary>
    public bool Segmentable { get; set; }
    
    /// <summary>
    /// Whether the link is locked
    /// </summary>
    public bool Locked { get; set; }
    
    /// <summary>
    /// Whether the link is visible
    /// </summary>
    public bool Visible { get; set; } = true;
    
    /// <summary>
    /// Whether the link is selected
    /// </summary>
    public bool Selected { get; set; }
    
    /// <summary>
    /// Custom properties as JSON
    /// </summary>
    public string? PropertiesJson { get; set; }
    
    /// <summary>
    /// Navigation properties
    /// </summary>
    [ForeignKey(nameof(DiagramId))]
    public virtual DiagramEntity? Diagram { get; set; }
    
    public virtual ICollection<LinkVertexEntity> Vertices { get; set; } = new List<LinkVertexEntity>();
    public virtual ICollection<LinkLabelEntity> Labels { get; set; } = new List<LinkLabelEntity>();
}
