using Blazor.Diagrams.Core.Persistence;
using Blazor.Diagrams.Persistence.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Blazor.Diagrams.Persistence;

/// <summary>
/// Unit of Work implementation for managing transactions and repositories
/// </summary>
public class UnitOfWork : IUnitOfWork
{
    private readonly DiagramDbContext _context;
    private IDbContextTransaction? _transaction;
    private bool _disposed;

    // Lazy-loaded repositories
    private IRepository<DiagramEntity>? _diagrams;
    private NodeRepository? _nodes;
    private LinkRepository? _links;
    private IRepository<PortEntity>? _ports;
    private IRepository<GroupEntity>? _groups;

    public UnitOfWork(DiagramDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    public IRepository<DiagramEntity> Diagrams => 
        _diagrams ??= new Repository<DiagramEntity>(_context);

    public IRepository<NodeEntity> Nodes => 
        _nodes ??= new NodeRepository(_context);

    public IRepository<LinkEntity> Links => 
        _links ??= new LinkRepository(_context);

    public IRepository<PortEntity> Ports => 
        _ports ??= new Repository<PortEntity>(_context);

    public IRepository<GroupEntity> Groups => 
        _groups ??= new Repository<GroupEntity>(_context);

    /// <summary>
    /// Get specialized node repository with additional methods
    /// </summary>
    public NodeRepository NodeRepository => 
        _nodes ??= new NodeRepository(_context);

    /// <summary>
    /// Get specialized link repository with additional methods
    /// </summary>
    public LinkRepository LinkRepository => 
        _links ??= new LinkRepository(_context);

    public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
            throw new InvalidOperationException("Transaction already started");

        _transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
    }

    public async Task CommitAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction == null)
            throw new InvalidOperationException("No transaction to commit");

        try
        {
            await SaveChangesAsync(cancellationToken);
            await _transaction.CommitAsync(cancellationToken);
        }
        catch
        {
            await RollbackAsync(cancellationToken);
            throw;
        }
        finally
        {
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction == null)
            throw new InvalidOperationException("No transaction to rollback");

        try
        {
            await _transaction.RollbackAsync(cancellationToken);
        }
        finally
        {
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Update timestamps for modified entities
        foreach (var entry in _context.ChangeTracker.Entries<IPersistable>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = DateTime.UtcNow;
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    break;
                case EntityState.Modified:
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    entry.Entity.Version++;
                    break;
            }
        }

        return await _context.SaveChangesAsync(cancellationToken);
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _transaction?.Dispose();
            _context.Dispose();
            _disposed = true;
        }
    }
}
