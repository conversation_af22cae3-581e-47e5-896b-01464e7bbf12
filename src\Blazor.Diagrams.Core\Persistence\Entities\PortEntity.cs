using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Blazor.Diagrams.Core.Persistence;

/// <summary>
/// Database entity representing a node port
/// </summary>
public class PortEntity : BaseEntity
{
    [Required]
    public string NodeId { get; set; } = string.Empty;
    
    /// <summary>
    /// Port alignment (Top, Right, Bottom, Left, etc.)
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string Alignment { get; set; } = string.Empty;
    
    /// <summary>
    /// Position X coordinate relative to node
    /// </summary>
    public double PositionX { get; set; }
    
    /// <summary>
    /// Position Y coordinate relative to node
    /// </summary>
    public double PositionY { get; set; }
    
    /// <summary>
    /// Width of the port
    /// </summary>
    public double Width { get; set; }
    
    /// <summary>
    /// Height of the port
    /// </summary>
    public double Height { get; set; }
    
    /// <summary>
    /// Whether the port is locked
    /// </summary>
    public bool Locked { get; set; }
    
    /// <summary>
    /// Whether the port is visible
    /// </summary>
    public bool Visible { get; set; } = true;
    
    /// <summary>
    /// Whether the port is initialized
    /// </summary>
    public bool Initialized { get; set; }
    
    /// <summary>
    /// Custom properties as JSON
    /// </summary>
    public string? PropertiesJson { get; set; }
    
    /// <summary>
    /// Navigation properties
    /// </summary>
    [ForeignKey(nameof(NodeId))]
    public virtual NodeEntity? Node { get; set; }
}
