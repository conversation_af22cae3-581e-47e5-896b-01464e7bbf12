﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <Authors>zHaytam</Authors>
    <Description>Algorithms for Z.Blazor.Diagrams</Description>
    <AssemblyVersion>3.0.3</AssemblyVersion>
    <FileVersion>3.0.3</FileVersion>
    <RepositoryUrl>https://github.com/zHaytam/Blazor.Diagrams</RepositoryUrl>
    <Version>3.0.3</Version>
    <PackageId>Z.Blazor.Diagrams.Algorithms</PackageId>
    <PackageTags>blazor diagrams diagramming svg drag algorithms layouts</PackageTags>
    <Product>Z.Blazor.Diagrams.Algorithms</Product>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Blazor.Diagrams.Core\Blazor.Diagrams.Core.csproj" />
  </ItemGroup>

</Project>
